package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.Concession;
import com.wexl.erp.fees.model.ConcessionHead;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ConcessionHeadRepository extends JpaRepository<ConcessionHead, Long> {
  List<ConcessionHead> findAllByConcessionAndOrgSlug(Concession concession, String orgSlug);

  Optional<ConcessionHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);
}
