package com.wexl.erp.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.dto.GuardianRequest;
import lombok.Builder;

public record MedicalProfile() {

  @Builder
  public record Request(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") <PERSON><PERSON> remarks) {}

  @Builder
  public record Response(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("drug_or_medicines") String drugOrMedicines,
      @JsonProperty("food_allergy") String foodAllergy,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("allergies") Allergies allergies,
      @JsonProperty("illness") Illness illness,
      @JsonProperty("chronic_diseases") String chronicDiseases,
      @JsonProperty("heart_condition") String heartCondition,
      @JsonProperty("surgery_or_admitted_hospital") String surgeryOrAdmittedHospital,
      @JsonProperty("wears_spectacles") String wearsSpectacles,
      @JsonProperty("dental_treatment") String dentalTreatment,
      @JsonProperty("remarks") Remarks remarks) {}

  @Builder
  public record Allergies(
      @JsonProperty("allergy_name") String allergyName,
      @JsonProperty("allergy_description") String allergyDescription) {}

  @Builder
  public record Illness(
      @JsonProperty("illness_name") String illnessName,
      @JsonProperty("illness_description") String illnessDescription) {}

  @Builder
  public record Remarks(
      @JsonProperty("remarks") String remarks) {}
}
